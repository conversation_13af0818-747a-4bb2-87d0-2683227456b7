import { Head } from '@inertiajs/react';
import { PublicNavigation } from '@/components/public-navigation';
import { PublicFooter } from '@/components/public-footer';
import { type ReactNode } from 'react';

interface PublicLayoutProps {
    children: ReactNode;
    title?: string;
    description?: string;
    className?: string;
}

export default function PublicLayout({
    children,
    title = 'BSG Support',
    description = 'Scale Smarter. Grow Faster. with Backsure Global Support',
    className = ''
}: PublicLayoutProps) {
    return (
        <>
            <Head title={title}>
                <meta name="description" content={description} />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            <div className={`min-h-screen flex flex-col ${className}`}>
                <PublicNavigation />
                <main className="flex-1">
                    {children}
                </main>
                {/* <PublicFooter /> */}
            </div>
        </>
    );
}
